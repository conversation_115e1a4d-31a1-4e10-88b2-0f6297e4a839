# 🌐 Deno Deploy部署指南

## 🚀 快速部署到Deno Deploy

### 1. 准备工作

1. **注册Deno Deploy账号**：
   - 访问 https://dash.deno.com/
   - 使用GitHub账号登录

2. **准备代码仓库**：
   - 将代码推送到GitHub仓库
   - 确保包含 `deno.deploy.ts` 文件

### 2. 创建新项目

1. **在Deno Deploy控制台**：
   - 点击 "New Project"
   - 选择 "Deploy from GitHub repository"

2. **连接GitHub仓库**：
   - 选择包含代码的仓库
   - 设置入口文件为：`main.ts`
   - 选择分支（通常是 `main` 或 `master`）

### 3. 配置环境变量

在Deno Deploy项目设置中添加以下环境变量：

| 变量名 | 值 | 说明 |
|--------|-----|------|
| `NETEASE_COOKIE` | `MUSIC_U=your_cookie;os=pc;appver=8.9.75;` | **必需** - 网易云音乐Cookie |
| `DEBUG` | `false` | 可选 - 调试模式 |
| `SEARCH_LIMIT` | `50` | 可选 - 搜索结果限制 |
| `DOWNLOAD_CONCURRENCY` | `3` | 可选 - 下载并发数 |

### 4. 获取网易云音乐Cookie

1. **登录网易云音乐**：
   - 访问 https://music.163.com/
   - 使用您的账号登录

2. **获取Cookie**：
   - 打开浏览器开发者工具 (F12)
   - 切换到 "Network" 标签
   - 刷新页面
   - 找到任意请求，查看请求头
   - 复制完整的 `Cookie` 字段内容

3. **设置环境变量**：
   - 在Deno Deploy控制台的项目设置中
   - 添加环境变量 `NETEASE_COOKIE`
   - 粘贴完整的Cookie内容

### 5. 部署

1. **自动部署**：
   - 配置完成后，Deno Deploy会自动部署
   - 等待部署完成（通常1-2分钟）

2. **获取访问地址**：
   - 部署成功后会获得一个 `.deno.dev` 域名
   - 例如：`https://your-project.deno.dev`

## 🔧 部署配置

### 项目结构

```
MusicApi-Deno/
├── deno.deploy.ts          # Deno Deploy入口文件 ⭐
├── netease-eapi.ts         # 核心EAPI实现
├── server.ts               # 本地开发服务器
├── config.ts               # 配置管理
├── deno.json               # Deno配置
└── README.md               # 文档
```

### 入口文件说明

- **`deno.deploy.ts`** - 专为Deno Deploy优化的入口文件
- **`server.ts`** - 本地开发使用的服务器文件

### 环境检测

代码会自动检测运行环境：
```typescript
const isDeployment = Deno.env.get('DENO_DEPLOYMENT_ID');
```

## 🌐 Deno Deploy优势

### 性能优势

- **全球CDN**：自动分发到全球边缘节点
- **零冷启动**：V8 Isolates技术，毫秒级启动
- **自动扩容**：根据流量自动扩展

### 开发体验

- **零配置**：无需Docker或复杂配置
- **实时部署**：Git推送后自动部署
- **实时日志**：在线查看运行日志

### 安全性

- **自动HTTPS**：免费SSL证书
- **沙箱隔离**：V8 Isolates安全隔离
- **权限控制**：细粒度权限管理

## 📊 监控和调试

### 查看日志

1. **在Deno Deploy控制台**：
   - 进入项目详情页
   - 点击 "Logs" 标签
   - 查看实时日志

2. **日志内容**：
   ```
   🦕 Deno Deploy网易云音乐解析服务启动
   🌐 部署ID: abc123-def456
   🔍 Cookie状态: ✅ 有效
   🎵 支持无损音质解析
   ```

### 性能监控

- **响应时间**：在控制台查看平均响应时间
- **请求量**：监控每日请求数量
- **错误率**：跟踪错误发生率

## 🔄 更新部署

### 自动更新

1. **推送代码**：
   ```bash
   git add .
   git commit -m "更新功能"
   git push origin main
   ```

2. **自动部署**：
   - Deno Deploy检测到代码变更
   - 自动触发重新部署
   - 通常1-2分钟完成

### 手动部署

1. **在控制台**：
   - 进入项目设置
   - 点击 "Redeploy"
   - 选择特定的commit进行部署

## 🎯 使用示例

### 访问部署的服务

```bash
# 解析歌曲
curl -X POST https://your-project.deno.dev/api/netease/song \
  -H "Content-Type: application/json" \
  -d '{"ids": "5257138", "level": "lossless"}'
```

### Web界面

直接访问：`https://your-project.deno.dev`

## 🔍 故障排除

### 常见问题

1. **部署失败**：
   - 检查 `deno.deploy.ts` 文件是否存在
   - 确认入口文件路径正确

2. **Cookie无效**：
   - 重新获取Cookie
   - 确认环境变量设置正确

3. **权限错误**：
   - Deno Deploy会自动处理权限
   - 无需手动设置 `--allow-*` 参数

### 调试技巧

1. **启用调试模式**：
   - 设置环境变量 `DEBUG=true`
   - 查看详细日志

2. **检查环境变量**：
   - 在代码中添加 `console.log(Deno.env.get('NETEASE_COOKIE'))`
   - 确认变量正确传递

## 🎉 部署成功

部署成功后，您将拥有：

✅ **全球访问**：通过CDN加速的全球访问  
✅ **自动HTTPS**：免费SSL证书  
✅ **零维护**：无需服务器管理  
✅ **实时更新**：代码推送自动部署  
✅ **高可用性**：99.9%的服务可用性  

享受Deno Deploy带来的现代化部署体验！🚀
