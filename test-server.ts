// 简化测试服务器
import { NeteaseEAPI, type QualityLevel, type SongInfo } from "./netease-eapi.ts";

const isDenoDeploy = false;
const config = {
  NETEASE_COOKIE: 'MUSIC_U=00FFA8CE389465AA732A7D4AEE103DE40DCD4F710617173BA6A56E167ACC84FB92A50621EEC48FB9BE0B8C0C3B9E7C01ACDF1BDF2E9537A78B46B9259E8CA466F98F88E7AA5050710946BFD37D354A3BB87C04FE2BAB5AE6EF21E4C97440548D952013C829AF7FFD559B959ED4B1DB7E9952327B7F96A9ED178D4C13E58B0CBC7EF63420C48DDC9613DBCA9596B242BB36884ECF79B2CADFB1F827887FFB465F16D2E71E12FAFA5D2202C9DCAE0BB64264B7992B2F0E8C22FC6D745E86E799193FBBEFA972CE210799EADED0415D3761C611301C7DF2F01B060170E968E8D9BAD05D8EE40E396445F65CF5D1D350D77127123DA0B34A3D3C89B13A0E390D490DF569017EB2FE96F61E16339FD964F797E180E841F14A59471FF484D74C50346EB205F62437FBFA1B7013A42E2B0BA20F5D2BA5BC5871F6E2D589C865773B281DD95226679B6A78BF2A657CB5B9E13364669432D4AF2BAA25560AAC0B1C3C650126DC86AB9E19C125A2D3DC767D165A54D1;os=pc;appver=8.9.75',
  PORT: 3003
};

const api = new NeteaseEAPI();

function getTestPage() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 功能测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        input { width: 300px; padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 网易云音乐解析器功能测试</h1>
        
        <div class="test-section">
            <h3>🎯 单曲解析测试</h3>
            <input type="text" id="songInput" placeholder="输入歌曲ID或分享链接" value="123456">
            <button class="test-button" onclick="testSongParse()">测试单曲解析</button>
            <div id="songResult"></div>
        </div>
        
        <div class="test-section">
            <h3>📦 批量下载测试</h3>
            <input type="text" id="albumInput" placeholder="输入专辑ID或分享链接" value="242274622">
            <button class="test-button" onclick="testAlbumParse()">测试专辑解析</button>
            <div id="albumResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 分享链接解析测试</h3>
            <button class="test-button" onclick="testLinkParsing()">测试所有链接格式</button>
            <div id="linkResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🍪 Cookie状态测试</h3>
            <button class="test-button" onclick="testCookie()">测试Cookie有效性</button>
            <div id="cookieResult"></div>
        </div>
    </div>

    <script>
        console.log('🧪 测试页面加载完成');
        
        async function testSongParse() {
            const input = document.getElementById('songInput').value;
            const resultDiv = document.getElementById('songResult');
            
            resultDiv.innerHTML = '<div class="result loading">🎯 正在测试单曲解析...</div>';
            
            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: input, level: 'lossless' })
                });
                
                const data = await response.json();
                
                if (data.status === 200) {
                    resultDiv.innerHTML = '<div class="result success">✅ 单曲解析成功<br>歌曲: ' + data.name + '<br>艺术家: ' + data.ar_name + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ 解析失败: ' + data.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ 请求失败: ' + error.message + '</div>';
            }
        }
        
        async function testAlbumParse() {
            const input = document.getElementById('albumInput').value;
            const resultDiv = document.getElementById('albumResult');
            
            resultDiv.innerHTML = '<div class="result loading">📦 正在测试专辑解析...</div>';
            
            try {
                const response = await fetch('/api/playlist-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'album', id: input })
                });
                
                const data = await response.json();
                
                if (data.status === 200) {
                    resultDiv.innerHTML = '<div class="result success">✅ 专辑解析成功<br>专辑: ' + data.name + '<br>歌曲数: ' + data.songs.length + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ 解析失败: ' + data.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ 请求失败: ' + error.message + '</div>';
            }
        }
        
        function testLinkParsing() {
            const resultDiv = document.getElementById('linkResult');
            
            const testLinks = [
                { type: '纯数字ID', link: '123456', expected: '123456' },
                { type: '完整歌曲链接', link: 'https://music.163.com/song?id=123456', expected: '123456' },
                { type: '移动端链接', link: 'https://music.163.com/m/song/123456', expected: '123456' },
                { type: '专辑链接', link: 'http://music.163.com/album/242274622/', expected: '242274622' },
                { type: '歌单链接', link: 'https://music.163.com/playlist/7091014698/', expected: '7091014698' }
            ];
            
            let results = '<div class="result success">🔗 链接解析测试结果:<br>';
            
            testLinks.forEach(test => {
                const extracted = extractId(test.link, test.type);
                const status = extracted === test.expected ? '✅' : '❌';
                results += status + ' ' + test.type + ': ' + test.link + ' → ' + extracted + '<br>';
            });
            
            results += '</div>';
            resultDiv.innerHTML = results;
        }
        
        function extractId(input, type) {
            // 简化的ID提取逻辑
            if (/^\d+$/.test(input)) return input;
            
            const songMatch = input.match(/music\.163\.com\/.*[?&]id=(\d+)|music\.163\.com\/m\/song\/(\d+)/);
            if (songMatch) return songMatch[1] || songMatch[2];
            
            const albumMatch = input.match(/album\/(\d+)/);
            if (albumMatch) return albumMatch[1];
            
            const playlistMatch = input.match(/playlist\/(\d+)/);
            if (playlistMatch) return playlistMatch[1];
            
            return null;
        }
        
        async function testCookie() {
            const resultDiv = document.getElementById('cookieResult');
            
            resultDiv.innerHTML = '<div class="result loading">🍪 正在测试Cookie状态...</div>';
            
            try {
                const response = await fetch('/api/cookie-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                
                if (data.valid) {
                    resultDiv.innerHTML = '<div class="result success">✅ Cookie有效<br>VIP状态: ' + (data.isVip ? 'VIP用户' : '普通用户') + '<br>剩余天数: ' + data.vipDaysLeft + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ Cookie无效: ' + data.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ 请求失败: ' + error.message + '</div>';
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.onload = function() {
            console.log('🧪 开始自动化测试...');
            setTimeout(testLinkParsing, 1000);
        };
    </script>
</body>
</html>`;
}

async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const pathname = url.pathname;

  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    if (pathname === '/') {
      return new Response(getTestPage(), {
        headers: { 'Content-Type': 'text/html; charset=utf-8', ...corsHeaders }
      });
    } else if (pathname === '/api/song' && req.method === 'POST') {
      const body = await req.json();
      const { ids, level = 'lossless' } = body;
      
      console.log('🎵 测试单曲解析:', ids);
      
      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);
      
      const result = await api.url_v1(ids, level as QualityLevel, cookies);
      
      if (!result?.data?.[0]) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲信息'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const songData = result.data[0];
      
      return new Response(JSON.stringify({
        status: 200,
        name: '测试歌曲',
        ar_name: '测试艺术家',
        url: songData.url,
        level: level
      }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
      
    } else if (pathname === '/api/playlist-info' && req.method === 'POST') {
      const body = await req.json();
      const { type, id } = body;
      
      console.log('📦 测试批量解析:', type, id);
      
      return new Response(JSON.stringify({
        status: 200,
        name: '测试专辑',
        songs: [{ id: '1', name: '测试歌曲1' }, { id: '2', name: '测试歌曲2' }]
      }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
      
    } else if (pathname === '/api/cookie-check' && req.method === 'POST') {
      console.log('🍪 测试Cookie状态');
      
      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);
      
      const isValid = await api.testCookie(cookies);
      
      return new Response(JSON.stringify({
        status: 200,
        valid: isValid,
        isVip: true,
        vipDaysLeft: 999
      }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
    
    return new Response('Not Found', { status: 404 });
    
  } catch (error) {
    console.error('❌ 服务器错误:', error);
    return new Response(JSON.stringify({
      status: 500,
      error: error.message
    }), {
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

console.log('🧪 测试服务器启动');
console.log('🌐 测试地址: http://localhost:3003');

Deno.serve({ port: config.PORT }, handler);
