/// <reference types="https://deno.land/x/types/index.d.ts" />

/**
 * Deno Deploy主入口文件
 * 统一的服务器实现，自动适配本地和Deno Deploy环境
 */

import { NeteaseEAPI, type QualityLevel, type SongInfo } from "./netease-eapi.ts";

// 环境检测
const isDenoDeploy = !!Deno.env.get('DENO_DEPLOYMENT_ID');

// 配置管理
interface Config {
  NETEASE_COOKIE: string;
  PORT: number;
  DEBUG: boolean;
  SEARCH_LIMIT: number;
  DOWNLOAD_CONCURRENCY: number;
}

// 加载配置
function loadConfig(): Config {
  const config: Config = {
    NETEASE_COOKIE: Deno.env.get('NETEASE_COOKIE') ||
      'MUSIC_U=00FFA8CE389465AA732A7D4AEE103DE40DCD4F710617173BA6A56E167ACC84FB92A50621EEC48FB9BE0B8C0C3B9E7C01ACDF1BDF2E9537A78B46B9259E8CA466F98F88E7AA5050710946BFD37D354A3BB87C04FE2BAB5AE6EF21E4C97440548D952013C829AF7FFD559B959ED4B1DB7E9952327B7F96A9ED178D4C13E58B0CBC7EF63420C48DDC9613DBCA9596B242BB36884ECF79B2CADFB1F827887FFB465F16D2E71E12FAFA5D2202C9DCAE0BB64264B7992B2F0E8C22FC6D745E86E799193FBBEFA972CE210799EADED0415D3761C611301C7DF2F01B060170E968E8D9BAD05D8EE40E396445F65CF5D1D350D77127123DA0B34A3D3C89B13A0E390D490DF569017EB2FE96F61E16339FD964F797E180E841F14A59471FF484D74C50346EB205F62437FBFA1B7013A42E2B0BA20F5D2BA5BC5871F6E2D589C865773B281DD95226679B6A78BF2A657CB5B9E13364669432D4AF2BAA25560AAC0B1C3C650126DC86AB9E19C125A2D3DC767D165A54D1;os=pc;appver=8.9.75;',
    PORT: parseInt(Deno.env.get('PORT') || (isDenoDeploy ? '8000' : '3002')),
    DEBUG: (Deno.env.get('DEBUG') || 'false').toLowerCase() === 'true',
    SEARCH_LIMIT: parseInt(Deno.env.get('SEARCH_LIMIT') || '50'),
    DOWNLOAD_CONCURRENCY: parseInt(Deno.env.get('DOWNLOAD_CONCURRENCY') || '3')
  };

  // 验证Cookie
  if (!config.NETEASE_COOKIE.includes('MUSIC_U=')) {
    console.error('错误：未配置有效的NETEASE_COOKIE');
    if (isDenoDeploy) {
      console.log('请在Deno Deploy控制台设置环境变量 NETEASE_COOKIE');
    } else {
      console.log('请设置环境变量：export NETEASE_COOKIE="your_cookie"');
    }
  }

  return config;
}

const config = loadConfig();
const api = new NeteaseEAPI();

// 主页HTML
const getHomePage = (): string => `
<!DOCTYPE html>
<html>
<head>
    <title>网易云音乐解析器</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
        }

        .tab-button {
            flex: 1;
            background: none;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        input, select, textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: transform 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            text-align: center;
        }

        .search-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-top: 20px;
        }

        .search-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .song-info {
            flex: 1;
        }

        .song-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .song-artist {
            color: #666;
            font-size: 14px;
        }

        .song-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            width: auto;
        }

        .play-btn {
            background: #28a745;
            color: white;
        }

        .download-btn {
            background: #007bff;
            color: white;
        }

        .audio-player {
            width: 100%;
            margin-top: 15px;
            border-radius: 5px;
        }

        .playlist-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-top: 20px;
        }

        .playlist-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .playlist-item:last-child {
            border-bottom: none;
        }

        .playlist-checkbox {
            width: 18px;
            height: 18px;
        }

        .playlist-info {
            flex: 1;
        }

        .playlist-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .playlist-artist {
            color: #666;
            font-size: 14px;
        }

        .select-all-btn {
            margin: 15px 0;
            background: #6c757d;
        }

        .progress-container {
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 5px;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
        }

        .progress-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .tabs {
                flex-direction: column;
            }

            .tab-button {
                margin-bottom: 5px;
            }

            .song-actions {
                flex-direction: column;
            }

            .action-btn {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>网易云音乐解析器</h1>
        
        <!-- 功能选项卡 -->
        <div class="tabs">
            <button class="tab-button active" onclick="window.showTab('search', this)">单曲解析</button>
            <button class="tab-button" onclick="window.showTab('batch', this)">批量下载</button>
        </div>

        <!-- 单曲解析 -->
        <div id="search" class="tab-content active">
            <div class="form-group">
                <label for="songId">歌曲ID或分享链接</label>
                <input type="text" id="songId" placeholder="输入歌曲ID或网易云音乐分享链接">
            </div>
            <div class="form-group">
                <label for="parseQuality">音质等级</label>
                <select id="parseQuality">
                    <option value="standard">标准音质 (128kbps)</option>
                    <option value="exhigh">极高音质 (320kbps)</option>
                    <option value="lossless" selected>无损音质 (FLAC)</option>
                    <option value="hires">Hi-Res音质 (24bit/96kHz)</option>
                    <option value="jymaster">母带音质 (24bit/192kHz)</option>
                </select>
            </div>
            <button onclick="window.parseSong()">解析歌曲</button>
            <div id="searchResults" class="search-results" style="display: none;"></div>
        </div>
        
        <!-- 批量下载 -->
        <div id="batch" class="tab-content">
            <div class="form-group">
                <label for="batchType">批量类型</label>
                <select id="batchType">
                    <option value="album">专辑ID</option>
                    <option value="playlist">歌单ID</option>
                </select>
            </div>
            <div class="form-group">
                <label for="batchInput">输入ID</label>
                <input type="text" id="batchInput" placeholder="请输入专辑或歌单ID">
            </div>
            <button onclick="window.loadPlaylist()">加载歌曲列表</button>

            <div id="playlistContainer" style="display: none;">
                <button class="select-all-btn" onclick="window.toggleSelectAll()">全选/取消全选</button>
                <div class="form-group">
                    <label for="batchLevel">音质等级</label>
                    <select id="batchLevel">
                        <option value="standard">标准音质 (128kbps)</option>
                        <option value="exhigh">极高音质 (320kbps)</option>
                        <option value="lossless" selected>无损音质 (FLAC)</option>
                        <option value="hires">Hi-Res音质 (24bit/96kHz)</option>
                        <option value="jymaster">母带音质 (24bit/192kHz)</option>
                    </select>
                </div>
                <div class="playlist-container" id="playlistItems"></div>
                <button onclick="window.downloadSelected()" style="margin-top: 15px;">下载选中歌曲</button>
            </div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        // 选项卡切换
        window.showTab = function(tabName, element) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            element.classList.add('active');
            document.getElementById('result').innerHTML = '';
        }

        // 直接解析单曲ID或分享链接
        window.parseSong = async function() {
            const input = document.getElementById('songId').value;
            const quality = document.getElementById('parseQuality').value;
            const resultDiv = document.getElementById('result');
            const searchResultsDiv = document.getElementById('searchResults');

            if (!input.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>请输入歌曲ID或分享链接</h3></div>';
                searchResultsDiv.style.display = 'none';
                return;
            }

            // 解析输入内容，支持多种格式
            let songId = '';
            const inputValue = input.trim();
            
            // 1. 检查是否为纯数字ID
            if (/^\\d+$/.test(inputValue)) {
                songId = inputValue;
            }
            // 2. 检查是否为网易云音乐分享链接
            else if (inputValue.includes('163cn.tv') || inputValue.includes('music.163.com')) {
                const longLinkMatch = inputValue.match(/song\\?id=(\\d+)/);
                if (longLinkMatch) {
                    songId = longLinkMatch[1];
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>无法识别的链接格式</h3><p>请输入纯数字的歌曲ID或有效的网易云音乐链接</p></div>';
                    searchResultsDiv.style.display = 'none';
                    return;
                }
            }
            // 3. 其他格式尝试提取数字
            else {
                const numberMatch = inputValue.match(/(\\d+)/);
                if (numberMatch) {
                    songId = numberMatch[1];
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>无法识别的格式</h3><p>请输入纯数字的歌曲ID或网易云音乐分享链接</p></div>';
                    searchResultsDiv.style.display = 'none';
                    return;
                }
            }

            // 最终验证提取的ID
            if (!songId || !/^\\d+$/.test(songId)) {
                resultDiv.innerHTML = '<div class="result error"><h3>提取的歌曲ID无效</h3><p>提取到的ID：' + songId + '</p></div>';
                searchResultsDiv.style.display = 'none';
                return;
            }

            // 显示加载状态
            resultDiv.innerHTML = '<div class="result loading"><h3>正在解析歌曲...</h3><p>歌曲ID: ' + songId + '</p></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: quality })
                });

                if (!response.ok) {
                    throw new Error('解析请求失败: ' + response.status + ' ' + response.statusText);
                }

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    const qualityText = quality === 'standard' ? '标准 (128k)' :
                                      quality === 'exhigh' ? '极高 (320k)' :
                                      quality === 'lossless' ? '无损 (FLAC)' :
                                      quality === 'hires' ? 'Hi-Res (24bit/96kHz)' :
                                      quality === 'jymaster' ? '母带 (24bit/192kHz)' : quality;

                    resultDiv.innerHTML = '<div class="result success"><h3>解析成功</h3><p>歌曲: ' + data.name + '</p><p>艺术家: ' + data.ar_name + '</p><p>音质: ' + qualityText + '</p></div>';

                    // 显示歌曲操作界面
                    const resultsHtml = '<div class="search-item"><div class="song-info"><div class="song-name">' + data.name + '</div><div class="song-artist">' + data.ar_name + '</div><div class="song-artist">专辑: ' + data.al_name + '</div><div class="song-artist">音质: ' + data.level + ' • 大小: ' + data.size + '</div></div><div class="song-actions"><button class="action-btn play-btn" onclick="window.playDirectUrl(\\'' + data.url + '\\', \\'' + data.name.replace(/'/g, "\\\\'") + '\\', \\'' + data.ar_name.replace(/'/g, "\\\\'") + '\\')">试听</button><button class="action-btn download-btn" onclick="window.downloadDirectUrl(\\'' + data.url + '\\', \\'' + data.name.replace(/'/g, "\\\\'") + '\\', \\'' + data.ar_name.replace(/'/g, "\\\\'") + '\\', \\'' + quality + '\\')">下载</button></div></div>';

                    searchResultsDiv.innerHTML = resultsHtml;
                    searchResultsDiv.style.display = 'block';

                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>解析失败</h3><p>' + (data.error || '无法获取歌曲信息') + '</p><p>歌曲ID: ' + songId + '</p></div>';
                    searchResultsDiv.style.display = 'none';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error"><h3>解析请求失败</h3><p>' + error.message + '</p><p>歌曲ID: ' + songId + '</p></div>';
                searchResultsDiv.style.display = 'none';
            }
        }

        // 直接播放URL
        window.playDirectUrl = function(url, songName, artist) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result success"><h3>正在播放: ' + songName + ' - ' + artist + '</h3><audio controls autoplay class="audio-player"><source src="' + url + '" type="audio/mpeg">您的浏览器不支持音频播放。</audio></div>';
        }

        // 直接下载URL
        window.downloadDirectUrl = function(url, songName, artist, quality) {
            const qualityText = quality === 'standard' ? '标准' :
                              quality === 'exhigh' ? '极高' :
                              quality === 'lossless' ? '无损' :
                              quality === 'hires' ? 'Hi-Res' :
                              quality === 'jymaster' ? '母带' : quality;
            
            const filename = songName + ' - ' + artist + ' [' + qualityText + '].' + (quality === 'lossless' || quality === 'hires' || quality === 'jymaster' ? 'flac' : 'mp3');
            
            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result success"><h3>开始下载</h3><p>文件名: ' + filename + '</p></div>';
        }

        // 加载歌单/专辑列表
        window.loadPlaylist = async function() {
            const batchType = document.getElementById('batchType').value;
            const batchInput = document.getElementById('batchInput').value;
            const resultDiv = document.getElementById('result');
            const playlistContainer = document.getElementById('playlistContainer');
            const playlistItems = document.getElementById('playlistItems');

            if (!batchInput.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>请输入ID</h3></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result loading"><h3>正在加载歌曲列表...</h3></div>';

            try {
                const response = await fetch('/api/playlist-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: batchType, id: batchInput.trim() })
                });

                const data = await response.json();

                if (data.status === 200) {
                    resultDiv.innerHTML = '<div class="result success"><h3>' + data.name + '</h3><p>共 ' + data.songs.length + ' 首歌曲</p></div>';

                    let itemsHtml = '';
                    data.songs.forEach((song, index) => {
                        itemsHtml += '<div class="playlist-item"><input type="checkbox" class="playlist-checkbox" id="song_' + song.id + '" checked><div class="playlist-info"><div class="playlist-name">' + song.name + '</div><div class="playlist-artist">' + song.artist + '</div></div></div>';
                    });

                    playlistItems.innerHTML = itemsHtml;
                    playlistContainer.style.display = 'block';

                    // 存储歌曲数据供下载使用
                    window.currentPlaylistSongs = data.songs;
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>加载失败</h3><p>' + (data.error || '未知错误') + '</p></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error"><h3>加载请求失败</h3><p>' + error.message + '</p></div>';
            }
        }

        // 全选/取消全选
        window.toggleSelectAll = function() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        // 下载选中歌曲
        window.downloadSelected = async function() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
            const selectedSongs = Array.from(checkboxes).map(cb => {
                const songId = cb.id.replace('song_', '');
                return window.currentPlaylistSongs.find(song => song.id.toString() === songId);
            }).filter(song => song);

            if (selectedSongs.length === 0) {
                document.getElementById('result').innerHTML = '<div class="result error"><h3>请选择要下载的歌曲</h3></div>';
                return;
            }

            const batchLevel = document.getElementById('batchLevel').value;
            const resultDiv = document.getElementById('result');

            // 创建进度条界面
            resultDiv.innerHTML = '<div class="result loading"><h3>批量下载进行中</h3><div class="progress-container"><div class="progress-bar"><div class="progress-fill" id="progressFill" style="width: 0%"></div></div><div class="progress-text" id="progressText">准备开始下载...</div><div class="progress-details"><span id="progressCurrent">0 / ' + selectedSongs.length + '</span><span id="progressStatus">成功: 0 | 失败: 0</span></div></div><p id="currentSong">正在准备下载队列...</p></div>';

            let successCount = 0;
            let failCount = 0;

            // 获取进度条元素
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressCurrent = document.getElementById('progressCurrent');
            const progressStatus = document.getElementById('progressStatus');
            const currentSong = document.getElementById('currentSong');

            // 逐个下载歌曲
            for (let i = 0; i < selectedSongs.length; i++) {
                const song = selectedSongs[i];

                try {
                    // 更新进度条
                    const progress = ((i) / selectedSongs.length) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = '正在下载第 ' + (i + 1) + ' 首，共 ' + selectedSongs.length + ' 首';
                    progressCurrent.textContent = (i + 1) + ' / ' + selectedSongs.length;
                    progressStatus.textContent = '成功: ' + successCount + ' | 失败: ' + failCount;
                    currentSong.textContent = song.artist + ' - ' + song.name;

                    const response = await fetch('/api/song', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            ids: song.id.toString(),
                            level: batchLevel
                        })
                    });

                    const data = await response.json();

                    if (data.status === 200 && data.url) {
                        // 获取文件扩展名
                        const fileExtension = batchLevel === 'lossless' || batchLevel === 'hires' || batchLevel === 'jymaster' ? 'flac' : 'mp3';
                        const filename = song.artist + ' - ' + song.name + '.' + fileExtension;

                        // 创建下载链接
                        const link = document.createElement('a');
                        link.href = data.url;
                        link.download = filename;
                        link.style.display = 'none';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        successCount++;
                    } else {
                        failCount++;
                    }

                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 500));

                } catch (error) {
                    failCount++;
                }
            }

            // 最终进度条更新
            progressFill.style.width = '100%';
            progressText.textContent = '下载完成！';
