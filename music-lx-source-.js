/**
 * @name 网易云音乐源
 * @description 基于网易云音乐API的高质量音源，支持无损FLAC和Hi-Res音频
 * @version 1.0.0
 * <AUTHOR> 4.0 sonnet
 * @homepage https://github.com/lyswhut/lx-music-desktop
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

// 配置你部署的服务器地址 - 请修改为你的实际地址
const BASE_URL = 'https://your-domain.com'

// 音质映射
const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'exhigh',
    'flac': 'lossless',
    'flac24bit': 'hires',
    'master': 'jymaster'  // 母带音质，需要SVIP
  }
}

// HTTP请求封装
const httpRequest = (url, options = {}) => new Promise((resolve, reject) => {
  const requestOptions = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    timeout: 15000,
    ...options
  }

  request(url, requestOptions, (err, resp) => {
    if (err) {
      console.error('[网易云音乐源] HTTP请求失败:', err.message)
      return reject(err)
    }
    try {
      const data = JSON.parse(resp.body)
      resolve(data)
    } catch (e) {
      console.error('[网易云音乐源] 响应解析失败:', resp.body)
      reject(new Error('响应解析失败'))
    }
  })
})

// API接口
const apis = {
  wy: {
    // 获取音乐播放链接
    async musicUrl(musicInfo, quality) {
      try {
        console.log(`[网易云音乐源] 获取音乐链接: ID=${musicInfo.songmid || musicInfo.id}, 音质=${quality}`)

        const response = await httpRequest(`${BASE_URL}/api/song`, {
          body: JSON.stringify({
            ids: musicInfo.songmid || musicInfo.id,
            level: quality
          })
        })

        if (response.status === 200 && response.url) {
          console.log(`[网易云音乐源] 获取链接成功: ${quality}`)
          return response.url
        } else {
          const error = response.error || '获取播放链接失败'
          console.error(`[网易云音乐源] 获取链接失败: ${error}`)
          throw new Error(error)
        }
      } catch (error) {
        console.error('[网易云音乐源] musicUrl异常:', error.message)
        throw error
      }
    },

    // 获取歌词
    async lyric(musicInfo) {
      try {
        console.log(`[网易云音乐源] 获取歌词: ID=${musicInfo.songmid || musicInfo.id}`)

        const response = await httpRequest(`${BASE_URL}/api/lyrics`, {
          body: JSON.stringify({
            id: musicInfo.songmid || musicInfo.id
          })
        })

        if (response.status === 200 && response.lyrics) {
          console.log('[网易云音乐源] 歌词获取成功')
          return {
            lyric: response.lyrics.lrc?.lyric || '',
            tlyric: response.lyrics.tlyric?.lyric || '',
            rlyric: null,
            lxlyric: null
          }
        }
      } catch (error) {
        console.error('[网易云音乐源] 歌词获取失败:', error.message)
      }

      return {
        lyric: '',
        tlyric: '',
        rlyric: null,
        lxlyric: null
      }
    },

    // 获取封面图片
    async pic(musicInfo) {
      const picUrl = musicInfo.img || musicInfo.pic || musicInfo.al?.picUrl || ''
      console.log(`[网易云音乐源] 获取封面: ${picUrl ? '成功' : '无封面'}`)
      return picUrl
    },

    // 搜索音乐（预留功能，如果落雪音乐支持）
    async search(keyword, page = 1) {
      try {
        console.log(`[网易云音乐源] 搜索: ${keyword}, 页码: ${page}`)

        const response = await httpRequest(`${BASE_URL}/api/search`, {
          body: JSON.stringify({
            keyword: keyword,
            type: 'songs'
          })
        })

        if (response.status === 200 && response.songs) {
          console.log(`[网易云音乐源] 搜索成功: 找到${response.songs.length}首歌曲`)
          return response.songs.map(song => ({
            id: song.id,
            songmid: song.id,
            name: song.name,
            singer: song.artists?.map(a => a.name).join('/') || '未知艺术家',
            album: song.album?.name || '',
            albumId: song.album?.id,
            img: song.album?.picUrl,
            duration: Math.floor(song.duration / 1000),
            source: 'wy'
          }))
        } else {
          console.log('[网易云音乐源] 搜索无结果')
          return []
        }
      } catch (error) {
        console.error('[网易云音乐源] 搜索失败:', error.message)
        return []
      }
    }
  }
}

// 注册API请求事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log(`[网易云音乐源] 收到请求: source=${source}, action=${action}`)

  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
        .catch(err => {
          console.error('[网易云音乐源] 获取音乐URL失败:', err.message)
          return Promise.reject(err)
        })

    case 'lyric':
      return apis[source].lyric(info.musicInfo)
        .catch(err => {
          console.error('[网易云音乐源] 获取歌词失败:', err.message)
          return Promise.reject(err)
        })

    case 'pic':
      return apis[source].pic(info.musicInfo)
        .catch(err => {
          console.error('[网易云音乐源] 获取封面失败:', err.message)
          return Promise.reject(err)
        })

    case 'search':
      return apis[source].search(info.keyword, info.page)
        .catch(err => {
          console.error('[网易云音乐源] 搜索失败:', err.message)
          return Promise.reject(err)
        })

    default:
      console.error(`[网易云音乐源] 不支持的操作: ${action}`)
      return Promise.reject(new Error(`不支持的操作: ${action}`))
  }
})

// 发送初始化完成事件
send(EVENT_NAMES.inited, {
  openDevTools: false, // 生产环境设为false，开发调试时可设为true
  sources: {
    wy: {
      name: '网易云音乐',
      type: 'music',
      actions: ['musicUrl', 'lyric', 'pic'], // 注意：search功能需要落雪音乐官方支持
      qualitys: ['128k', '320k', 'flac', 'flac24bit', 'master'] // 支持母带音质
    }
  }
})

console.log('[网易云音乐源] 初始化完成，支持音质:', ['128k', '320k', 'flac', 'flac24bit', 'master'])
