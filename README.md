# 🦕 网易云音乐API - Deno TypeScript版本

基于Deno运行时的现代TypeScript实现，支持网易云音乐无损音质解析。

## ✨ 特性

- 🦕 **Deno运行时** - 现代、安全的JavaScript/TypeScript运行时
- 📘 **TypeScript原生支持** - 无需编译，直接运行TypeScript代码
- 🔐 **完整EAPI加密** - 精确移植Python版本的加密算法
- 🎧 **无损音质支持** - 支持从标准到超清母带的所有音质等级
- 🔍 **在线搜索** - 支持歌曲、歌手、专辑搜索
- 📦 **批量下载** - 支持专辑、歌单批量下载
- 🔧 **环境变量配置** - 灵活的配置管理
- 🌐 **现代Web界面** - 响应式设计，选项卡式操作
- ⚡ **高性能** - 基于V8引擎和Rust构建的高性能运行时
- 🔒 **安全性** - Deno的权限系统提供更好的安全性

## 🎯 支持的音质等级

| 音质等级 | 比特率 | 格式 | 说明 |
|---------|--------|------|------|
| standard | 128kbps | MP3 | 标准音质 |
| exhigh | 320kbps | MP3 | 极高音质 |
| **lossless** | **999kbps+** | **FLAC** | **无损音质** |
| hires | 1999kbps+ | FLAC | Hi-Res音质 |
| sky | 1999kbps+ | FLAC | 沉浸环绕声 |
| jyeffect | 999kbps+ | FLAC | 高清环绕声 |
| jymaster | 1999kbps+ | FLAC | 超清母带 |

## 🚀 快速开始

### 前置要求

- [Deno](https://deno.land/) 1.37+

### 安装Deno

**Windows (PowerShell):**
```powershell
irm https://deno.land/install.ps1 | iex
```

**macOS/Linux:**
```bash
curl -fsSL https://deno.land/install.sh | sh
```

### 运行项目

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd MusicApi-Deno
   ```

2. **配置Cookie**

   设置环境变量：
   ```bash
   export NETEASE_COOKIE="MUSIC_U=your_cookie;os=pc;appver=8.9.75;"
   ```

3. **启动服务器**
   ```bash
   deno task start
   ```
   
   或者开发模式（自动重载）：
   ```bash
   deno task dev
   ```

4. **访问Web界面**
   
   打开浏览器访问：`http://localhost:3002`

### 运行测试

```bash
deno task test
```

### 编译为可执行文件

```bash
deno task compile
```

## 📡 API接口

### POST /api/netease/song

解析网易云音乐歌曲，获取下载链接。

**请求体：**
```json
{
  "ids": "5257138",
  "level": "lossless"
}
```

**响应：**
```json
{
  "status": 200,
  "name": "歌曲名称",
  "ar_name": "歌手名称",
  "al_name": "专辑名称",
  "level": "无损音质",
  "size": "15.23MB",
  "url": "https://music.163.com/...",
  "br": 999000,
  "note": "🎉 成功获取无损音质! 比特率: 999000",
  "debug": {
    "requestedLevel": "lossless",
    "actualBr": 999000,
    "isLossless": true,
    "bitrateQuality": "无损音质"
  }
}
```

### POST /api/netease/search

搜索歌曲或歌手。

**请求体：**
```json
{
  "keyword": "周杰伦",
  "type": "songs"
}
```

**响应：**
```json
{
  "status": 200,
  "type": "songs",
  "total": 100,
  "songs": [
    {
      "id": 186016,
      "name": "不能说的秘密",
      "artists": [{"id": 6452, "name": "周杰伦"}],
      "album": {"id": 18903, "name": "不能说的秘密 电影原声带"}
    }
  ]
}
```

### POST /api/netease/batch

批量下载歌曲、专辑或歌单。

**歌曲列表批量下载：**
```json
{
  "type": "songs",
  "songIds": ["5257138", "186016", "27646205"],
  "level": "lossless"
}
```

**专辑批量下载：**
```json
{
  "type": "album",
  "albumId": "18903",
  "level": "lossless"
}
```

**歌单批量下载：**
```json
{
  "type": "playlist",
  "playlistId": "123456789",
  "level": "lossless"
}
```

**响应：**
```json
{
  "status": 200,
  "total": 10,
  "success": 8,
  "failed": 2,
  "results": [
    {
      "id": 5257138,
      "name": "歌曲名称",
      "artist": "歌手名称",
      "success": true,
      "url": "https://music.163.com/...",
      "size": 15728640,
      "br": 999000
    }
  ]
}
```

## 🔧 技术架构

### 核心组件

- **NeteaseEAPI类** - 网易云EAPI加密和请求处理
- **TypeScript类型定义** - 完整的类型安全支持
- **Deno原生HTTP服务器** - 基于Deno.serve的高性能HTTP服务
- **现代Web界面** - 响应式设计，支持移动端

### 加密算法

精确移植Python版本的EAPI加密算法：

1. **MD5哈希** - 使用Web Crypto API
2. **AES-128-ECB加密** - 通过CBC模式配合零IV实现
3. **PKCS7填充** - 标准的块加密填充
4. **参数格式** - 与Python版本完全一致

### Deno优势

- **TypeScript原生支持** - 无需编译步骤
- **现代ES模块** - 支持顶级await和ES模块
- **内置工具** - 格式化、测试、打包等工具
- **安全性** - 默认安全，需要显式授权权限
- **性能** - 基于V8和Rust的高性能运行时

## 🎵 使用示例

### 基础用法

```typescript
import { NeteaseEAPI } from "./netease-eapi.ts";

const api = new NeteaseEAPI();
const cookies = api.createFullCookieObject(
  api.parseCookie("MUSIC_U=your_cookie;os=pc;appver=8.9.75;")
);

// 获取无损音质
const result = await api.url_v1("5257138", "lossless", cookies);
console.log(result);
```

### 检查音质

```typescript
if (api.isLosslessQuality(result.data[0].br)) {
  console.log("🎉 成功获取无损音质!");
}
```

### 获取歌曲详情

```typescript
const songInfo = await api.getSongDetail("5257138");
console.log(`歌曲: ${songInfo.name} - ${songInfo.ar.map(a => a.name).join('/')}`);
```

## 🔍 调试和故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保给予必要的权限
   deno run --allow-net --allow-read server.ts
   ```

2. **Cookie配置错误**
   - 确保包含完整的`MUSIC_U`值
   - 检查Cookie是否过期

3. **歌曲无法解析**
   - 尝试不同的歌曲ID
   - 检查歌曲是否有版权限制

### 调试模式

启动时会显示详细的系统信息：
```
🦕 Deno TypeScript网易云音乐解析服务器启动中...
📡 服务地址: http://localhost:3002
🎵 API端点: http://localhost:3002/api/netease/song
🔍 Cookie状态: ✅ 包含会员Cookie
🦕 Deno版本: 1.37.0
📘 TypeScript版本: 5.2.2
⚡ V8版本: ***********
```

## 🚀 部署

### 🌐 Deno Deploy部署（推荐）

**一键部署到全球CDN**：

1. **准备代码仓库**：
   ```bash
   git clone <repository-url>
   cd MusicApi-Deno
   git push origin main
   ```

2. **部署到Deno Deploy**：
   - 访问 https://dash.deno.com/
   - 创建新项目，连接GitHub仓库
   - 设置入口文件：`deno.deploy.ts`
   - 配置环境变量：`NETEASE_COOKIE`

3. **获取访问地址**：
   - 自动获得 `https://your-project.deno.dev` 域名
   - 全球CDN加速访问

**详细部署指南**：查看 [DEPLOY.md](./DEPLOY.md)

### 💻 本地部署

```bash
# 本地开发
deno task start

# 测试Deno Deploy版本
deno task deploy
```

### 📦 编译部署

```bash
# 编译本地版本
deno task compile

# 编译Deno Deploy版本
deno task compile-deploy

# 运行编译后的文件
./music-api
```

### 🐳 Docker部署

```dockerfile
FROM denoland/deno:1.37.0

WORKDIR /app
COPY . .

# 设置环境变量
ENV NETEASE_COOKIE="your_cookie_here"

EXPOSE 8000

CMD ["deno", "run", "--allow-net", "--allow-env", "deno.deploy.ts"]
```

## 📊 性能对比

| 运行时 | 启动时间 | 内存占用 | 响应时间 | TypeScript支持 |
|--------|----------|----------|----------|----------------|
| **Deno** | **快** | **低** | **快** | **原生** |
| Node.js | 中等 | 中等 | 快 | 需要编译 |
| Python | 慢 | 高 | 中等 | 不支持 |

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## ⚠️ 免责声明

本项目仅供学习和研究使用，请遵守相关法律法规和服务条款。

## 🔗 相关链接

- [Deno官网](https://deno.land/)
- [TypeScript文档](https://www.typescriptlang.org/)
- [网易云音乐](https://music.163.com/)
