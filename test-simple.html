<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 JavaScript测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        input { width: 300px; padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 JavaScript功能测试</h1>
        
        <div>
            <h3>🎯 单曲解析测试</h3>
            <input type="text" id="songInput" placeholder="输入歌曲ID或分享链接" value="http://163cn.tv/Gng7M90">
            <button class="test-button" onclick="testParseSong()">测试解析</button>
            <div id="result"></div>
        </div>
        
        <div>
            <h3>🔧 函数测试</h3>
            <button class="test-button" onclick="testFunctions()">测试所有函数</button>
            <div id="functionResult"></div>
        </div>
    </div>

    <script>
        console.log('🧪 测试页面加载完成');
        
        // 测试单曲解析功能
        async function testParseSong() {
            const input = document.getElementById('songInput').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result">🎯 正在测试单曲解析...</div>';
            
            try {
                let songId = null;
                const trimmedInput = input.trim();
                
                console.log('🎯 开始解析输入:', trimmedInput);
                
                // 提取歌曲ID的函数
                function extractSongId(input) {
                    // 1. 检查是否为纯数字ID
                    if (/^\d+$/.test(input)) {
                        return input;
                    }
                    
                    // 2. 提取短链接中的ID (如: http://163cn.tv/Gng7M90)
                    const shortLinkMatch = input.match(/163cn\.tv\/([A-Za-z0-9]+)/);
                    if (shortLinkMatch) {
                        return shortLinkMatch[1]; // 返回短码，需要进一步解析
                    }
                    
                    // 3. 提取完整链接中的ID (如: https://music.163.com/song?id=123456)
                    const fullLinkMatch = input.match(/music\.163\.com\/.*[?&]id=(\d+)/);
                    if (fullLinkMatch) {
                        return fullLinkMatch[1];
                    }
                    
                    // 4. 提取移动端链接中的ID (如: https://music.163.com/m/song/123456)
                    const mobileLinkMatch = input.match(/music\.163\.com\/m\/song\/(\d+)/);
                    if (mobileLinkMatch) {
                        return mobileLinkMatch[1];
                    }
                    
                    return null;
                }
                
                const extractedId = extractSongId(trimmedInput);
                
                if (extractedId) {
                    // 如果提取到的是短码，需要通过API解析
                    if (!/^\d+$/.test(extractedId)) {
                        console.log('🎯 检测到短链接码:', extractedId);
                        // 调用短链接解析API
                        const shortLinkResponse = await fetch('/api/resolve-short-link', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ shortCode: extractedId })
                        });
                        
                        if (shortLinkResponse.ok) {
                            const shortLinkData = await shortLinkResponse.json();
                            if (shortLinkData.status === 200 && shortLinkData.songId) {
                                songId = shortLinkData.songId;
                                console.log('🎯 短链接解析成功，歌曲ID:', songId);
                            } else {
                                throw new Error('短链接解析失败: ' + (shortLinkData.error || '未知错误'));
                            }
                        } else {
                            throw new Error('短链接解析请求失败');
                        }
                    } else {
                        songId = extractedId;
                        console.log('🎯 提取到歌曲ID:', songId);
                    }
                } else {
                    throw new Error('无法识别输入格式，请输入网易云分享链接或歌曲ID');
                }

                // 获取歌曲详情和下载链接
                const songResponse = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: 'lossless' })
                });

                const songData = await songResponse.json();

                if (songData.status === 200 && songData.url) {
                    resultDiv.innerHTML = '<div class="result success">' +
                        '<h3>✅ 解析成功！</h3>' +
                        '<p><strong>歌曲：</strong>' + songData.name + '</p>' +
                        '<p><strong>艺术家：</strong>' + songData.ar_name + '</p>' +
                        '<p><strong>歌曲ID：</strong>' + songId + '</p>' +
                        '<p><strong>音质：</strong>' + songData.level + '</p>' +
                        '<p><strong>大小：</strong>' + songData.size + '</p>' +
                        '<p><strong>下载链接：</strong><a href="' + songData.url + '" target="_blank">点击下载</a></p>' +
                    '</div>';
                } else {
                    throw new Error(songData.error || '无法获取歌曲信息');
                }

            } catch (error) {
                console.error('❌ 解析失败:', error);
                resultDiv.innerHTML = '<div class="result error">' +
                    '<h3>❌ 解析失败</h3>' +
                    '<p>' + error.message + '</p>' +
                '</div>';
            }
        }
        
        // 测试所有函数
        function testFunctions() {
            const resultDiv = document.getElementById('functionResult');
            
            let results = '<div class="result success"><h3>🔧 函数测试结果：</h3>';
            
            // 测试函数是否存在
            results += '<p>✅ testParseSong: ' + (typeof testParseSong === 'function' ? '存在' : '不存在') + '</p>';
            results += '<p>✅ testFunctions: ' + (typeof testFunctions === 'function' ? '存在' : '不存在') + '</p>';
            
            // 测试全局对象
            results += '<p>✅ window对象: ' + (typeof window !== 'undefined' ? '存在' : '不存在') + '</p>';
            results += '<p>✅ fetch函数: ' + (typeof fetch === 'function' ? '存在' : '不存在') + '</p>';
            
            results += '</div>';
            
            resultDiv.innerHTML = results;
        }
        
        // 页面加载完成后自动运行测试
        window.onload = function() {
            console.log('🧪 开始自动化测试...');
            testFunctions();
        };
    </script>
</body>
</html>
